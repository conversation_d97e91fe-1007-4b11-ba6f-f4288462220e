'use client';

import * as React from "react"
import {useCookies} from "react-cookie";

export function useLandingSource() {
  const [cookies] = useCookies(['landingSource']);
  
  const getLandingSource = React.useCallback(() => {
    // Check cookie first, then localStorage as fallback
    return cookies.landingSource || (typeof window !== 'undefined' ? localStorage.getItem('landingSource') : null);
  }, [cookies.landingSource]);

  const clearLandingSource = React.useCallback(() => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('landingSource');
    }
  }, []);

  return {
    landingSource: getLandingSource(),
    clearLandingSource
  };
}
